import StoreLayout from "@/components/features/store/layout";
import { ProductCard } from "@/components/features/store/product-card";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { cn, parseProductOptions } from "@/lib/utils";
import { useGetAllCategoriesQuery } from "@/queries/admin-queries";
import { useGetCustomerQuery } from "@/queries/customer-queries";
import {
  useGetPaginatedProductsQuery,
  useGetPublicProductsQuery,
} from "@/queries/product-queries";
import useAuthStore from "@/stores/auth-store";
import { ProductOptions } from "@/supabase/types";
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  PackageX,
  Search,
} from "lucide-react";
import Head from "next/head";
import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import { CiSearch } from "react-icons/ci";

const ITEMS_PER_PAGE = 15; // defaults to 10 items per page
const DEBOUNCE_DELAY = 500; // milliseconds

/**
 * TEMPORARY SOLUTION: Custom product order for valve products
 *
 * This is a temporary frontend-only solution to display specific valve SKUs in a custom order.
 * A proper implementation should be done on the backend with a dedicated "display_order" field
 * or similar approach.
 *
 * TODO: Replace with backend sorting once available and remove this temporary solution.
 *
 * @updated: June 28, 2024
 * @requestedBy: Client - Special order for commercial valve products
 */
// TODO: sort by date of creation
const PRIORITY_SKUS = [
  // First, exact full SKUs in priority order
  "UC4",
  "UC4E1",
  "UC4E2",
  "UC4M",
  "UC4ME1",
  "UC4ME2",
  "UC4MR",
  "UC4MRE1",
  "UC4MRE2",
  "UC1A",
  "UC2A",
  "UC4MS2",
  "UC4S2",
].map((sku) => sku.toUpperCase());

/**
 * Helper function to sort products by SKU priority
 * Part of temporary solution for custom valve product ordering
 */
function sortProductsBySkuPriority(products: any[]) {
  if (!products || products.length === 0) return [];

  // Debug logging can be removed in production
  console.log(
    "Original SKU order:",
    products.map((p) => p.sku)
  );

  // Deep clone the products to avoid modifying the original array
  const result = [...products].sort((a, b) => {
    const skuA = (a.sku || "").trim().toUpperCase();
    const skuB = (b.sku || "").trim().toUpperCase();

    // Direct match with our priority SKUs
    const indexA = PRIORITY_SKUS.indexOf(skuA);
    const indexB = PRIORITY_SKUS.indexOf(skuB);

    // If both have exact matches in our priority list
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    // If only one has an exact match
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    // If no exact matches, look for partial matches in our priority SKUs
    // Finding the most specific matching prefix
    let bestMatchA = -1;
    let bestMatchB = -1;

    for (let i = 0; i < PRIORITY_SKUS.length; i++) {
      if (
        skuA.startsWith(PRIORITY_SKUS[i]) &&
        (bestMatchA === -1 ||
          PRIORITY_SKUS[i].length > PRIORITY_SKUS[bestMatchA].length)
      ) {
        bestMatchA = i;
      }
      if (
        skuB.startsWith(PRIORITY_SKUS[i]) &&
        (bestMatchB === -1 ||
          PRIORITY_SKUS[i].length > PRIORITY_SKUS[bestMatchB].length)
      ) {
        bestMatchB = i;
      }
    }

    // Compare best partial matches
    if (bestMatchA !== -1 && bestMatchB !== -1) {
      return bestMatchA - bestMatchB;
    }

    // If only one has a partial match
    if (bestMatchA !== -1) return -1;
    if (bestMatchB !== -1) return 1;

    // If no matches at all, maintain original order
    return 0;
  });

  // Debug logging can be removed in production
  console.log(
    "Sorted SKU order:",
    result.map((p) => p.sku)
  );

  return result;
}

export function ProductCardSkeleton() {
  return (
    <Card className="h-full min-w-[320px] flex flex-col justify-between border border-gray-200 shadow-sm transition-all duration-200">
      <div className="relative aspect-square p-4">
        <Skeleton className="w-full h-64 object-cover" />
      </div>
    </Card>
  );
}

export default function Products() {
  const router = useRouter();
  const { category } = router.query;

  return (
    <StoreLayout>
      <Head>
        <title>Products | {category}</title>
      </Head>
      <section>
        <ProductGrid />
      </section>
    </StoreLayout>
  );
}

interface Category {
  name: string;
  subCategories: Category[];
}

function ProductGrid() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [shouldLoadSkeleton, loadSkeleton] = useState(true);
  const [isCategoryChanging, setIsCategoryChanging] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const userData = useAuthStore((state) => state.data);
  const userId = userData?.id;
  const token = useAuthStore((state) => state.token);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();

  const allCategories = useGetAllCategoriesQuery(1, 100, token);

  // Keep track of all unique category names regardless of filtering
  const [allCachedCategories, setAllCachedCategories] = useState<string[]>([]);

  // Keep track of public category structure
  const [cachedPublicCategories, setCachedPublicCategories] = useState<
    Category[]
  >([]);

  const {
    data: customerData,
    isLoading: _isCustomerLoading,
    isError: _isCustomerError,
  } = useGetCustomerQuery(userId);

  const hasRestrictions =
    isAuthenticated &&
    customerData?.customer?.categories &&
    customerData.customer.categories.length > 0;

  const customerAssignedCategories = customerData?.customer?.categories ?? [];
  const customerAssignedCategoryIds = customerAssignedCategories.map(
    (category) => category.id
  );
  const customerAssignedCategoryNames = customerAssignedCategories.map(
    (category) => category.name ?? ""
  );

  const parentCategories =
    allCategories.data?.categories?.filter(
      (category) => category.parent_category_id === null
    ) ?? [];
  const categoryIds =
    allCategories.data?.categories?.map((category) => category.id) ?? [];
  const categoryNames =
    allCategories.data?.categories?.map((category) => category.name) ?? [];
  const subcategories =
    allCategories.data?.categories?.filter(
      (category) => category.parent_category_id !== null
    ) ?? [];
  const subCategoryIds = subcategories?.map((category) => category.id) ?? [];
  const subCategoryNames =
    subcategories?.map((category) => category.name ?? "") ?? [];

  const categories: Category[] = useMemo(() => {
    const categoryMap = new Map<string, Category>();

    // loop all through subcategories and find the parent category by parent_category_id field
    // and add it to the parent category
    subcategories.forEach((sub) => {
      const parentCategory = parentCategories.find(
        (cat) => cat.id === sub.parent_category_id
      );
      if (parentCategory) {
        if (!categoryMap.has(parentCategory.name ?? "")) {
          categoryMap.set(parentCategory.name ?? "", {
            name: parentCategory.name ?? "",
            subCategories: [],
          });
        }

        categoryMap
          .get(parentCategory.name ?? "")
          ?.subCategories.push({ name: sub.name ?? "", subCategories: [] });
      }
    });

    // populate the categories without subcategories
    categoryNames.forEach((name) => {
      if (!name) return;
      if (!categoryMap.has(name)) {
        categoryMap.set(name, { name, subCategories: [] });
      }
    });

    // Get the result but no longer need to sort subcategories
    const result = Array.from(categoryMap.values());

    // Sort only the main categories themselves
    return result.sort((a, b) => a.name.localeCompare(b.name));
  }, [categoryIds, categoryNames, subCategoryIds, subCategoryNames]);

  // Use appropriate query based on authentication status
  const { data: authenticatedProductsData, isLoading: isAuthProductsLoading } =
    useGetPaginatedProductsQuery(
      currentPage,
      ITEMS_PER_PAGE,
      token,
      activeCategory || undefined,
      debouncedSearchTerm || undefined
    );

  const { data: publicProductsData, isLoading: isPublicProductsLoading } =
    useGetPublicProductsQuery(
      currentPage,
      ITEMS_PER_PAGE,
      activeCategory || undefined,
      debouncedSearchTerm || undefined
    );

  // Determine which data source to use based on authentication status
  const productsData = isAuthenticated
    ? authenticatedProductsData
    : publicProductsData;

  const isLoading = isAuthenticated
    ? isAuthProductsLoading
    : isPublicProductsLoading;

  const products = productsData?.products || [];

  const totalItems = productsData?.total || 0;
  const totalPages =
    productsData?.totalPages || Math.ceil((totalItems || 0) / ITEMS_PER_PAGE);

  // Update cached categories whenever we get new ones from the API
  useEffect(() => {
    if (publicProductsData?.categories) {
      const categoryNames = publicProductsData.categories
        .map((cat: any) => cat.name)
        .filter(Boolean);

      setAllCachedCategories((prev) =>
        Array.from(new Set([...prev, ...categoryNames]))
      );
    }
  }, [publicProductsData?.categories]);

  const allCategoryNames = useMemo(() => {
    if (isAuthenticated) {
      return categoryNames;
    }

    // For unauthenticated users, use the cached categories if available
    if (allCachedCategories.length > 0) {
      return allCachedCategories;
    }

    // Fallback to extracting categories from products if API doesn't provide them
    const uniqueCategories = new Set<string>();
    products?.forEach((product) => {
      product.product_categories?.forEach((cat) => {
        if (cat.category_id?.name) {
          uniqueCategories.add(cat.category_id.name);
        }
      });
    });

    return Array.from(uniqueCategories);
  }, [
    products,
    hasRestrictions,
    categoryNames,
    isAuthenticated,
    allCachedCategories,
  ]);

  // Replace the debounce search effect with a form submit handler
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Set the debounced search term directly (no delay)
    setDebouncedSearchTerm(searchTerm);

    // Reset to page 1 when search is submitted
    setCurrentPage(1);

    // Update URL with search term
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...(activeCategory ? { category: activeCategory } : {}),
          ...(searchTerm ? { search: searchTerm } : {}),
          page: "1", // Reset page to 1 in URL
        },
      },
      undefined,
      { shallow: true }
    );
  };

  // Clear search handler
  const clearSearch = () => {
    setSearchTerm("");
    setDebouncedSearchTerm("");

    // Update URL without search param
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...(activeCategory ? { category: activeCategory } : {}),
          page: currentPage.toString(),
        },
      },
      undefined,
      { shallow: true }
    );
  };

  // Fix the pagination functions to prevent overriding by the debounce effect
  const nextPageFn = () => {
    const newPage = Math.min(currentPage + 1, totalPages);

    // First update local state
    setCurrentPage(newPage);

    // Then update URL - important to use the newPage variable directly
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...(activeCategory ? { category: activeCategory } : {}),
          ...(debouncedSearchTerm ? { search: debouncedSearchTerm } : {}),
          page: newPage.toString(),
        },
      },
      undefined,
      { shallow: true }
    );
  };

  const prevPageFn = () => {
    const newPage = Math.max(currentPage - 1, 1);

    // First update local state
    setCurrentPage(newPage);

    // Then update URL - important to use the newPage variable directly
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...(activeCategory ? { category: activeCategory } : {}),
          ...(debouncedSearchTerm ? { search: debouncedSearchTerm } : {}),
          page: newPage.toString(),
        },
      },
      undefined,
      { shallow: true }
    );
  };

  const productSkeletons = Array(3)
    .fill(<></>)
    .map((_, index) => {
      const id = `product-skeleton-${index}`;
      return <ProductCardSkeleton key={id} />;
    });

  useEffect(
    function unloadSkeletons() {
      if (isLoading || products.length === 0) {
        const timeout = setTimeout(() => {
          loadSkeleton((_) => false);
        }, 1000);

        return () => clearTimeout(timeout);
      }
      loadSkeleton((_) => false);
    },
    [isLoading, products]
  );

  // Initialize active category and page from URL on mount
  useEffect(
    function initializeProducts() {
      if (router.isReady) {
        const { category, page, search } = router.query;

        // Set active category from URL
        if (category && typeof category === "string") {
          setActiveCategory(category);
        }

        // Set current page from URL
        if (page && typeof page === "string") {
          const pageNum = parseInt(page, 10);
          if (!isNaN(pageNum) && pageNum > 0) {
            setCurrentPage(pageNum);
          }
        }

        // Set search term from URL
        if (search && typeof search === "string") {
          setSearchTerm(search);
          // Important: Also set the debounced search term to match
          setDebouncedSearchTerm(search);
        }
      }
    },
    [router.isReady, router.query]
  );

  // Handle category expansion/collapse without changing products display
  const handleCategoryExpansion = (categoryName: string) => {
    // This function intentionally does not change activeCategory
    // It's only used for expanding/collapsing categories in the sidebar
    console.log(`Toggling expansion for category: ${categoryName}`);
    // The actual toggle is handled in the CategoryTree component
  };

  // Update URL when active category changes with improved handling
  const handleCategoryChange = (category: string | null) => {
    // Set loading state for category change
    setIsCategoryChanging(true);

    // Important: Set page to 1 BEFORE changing the category
    setCurrentPage(1);

    // Then update the category
    setActiveCategory(category);

    // Update URL query params
    const query = {
      ...(category === null ? {} : { category }),
      ...(debouncedSearchTerm ? { search: debouncedSearchTerm } : {}),
      page: "1", // Reset page to 1 in URL
    };

    router.push(
      {
        pathname: router.pathname,
        query,
      },
      undefined,
      { shallow: true }
    );

    // Clear the loading state after a short delay to allow for data to load
    setTimeout(() => {
      setIsCategoryChanging(false);
    }, 300);
  };

  // Construct categories tree for non-authenticated users
  const publicCategories: Category[] = useMemo(() => {
    if (isAuthenticated) return categories;

    const categoryMap = new Map<string, Category>();

    if (publicProductsData?.categories) {
      // First pass: create parent categories
      publicProductsData.categories.forEach((cat) => {
        if (!cat.parent_category_id) {
          categoryMap.set(cat.name, { name: cat.name, subCategories: [] });
        }
      });

      // Second pass: add subcategories to their parents
      publicProductsData.categories.forEach((cat) => {
        if (cat.parent_category_id) {
          const parentCat = publicProductsData.categories.find(
            (p) => p.id === cat.parent_category_id
          );
          if (parentCat && categoryMap.has(parentCat.name)) {
            categoryMap.get(parentCat.name)?.subCategories.push({
              name: cat.name,
              subCategories: [],
            });
          }
        }
      });
    }

    // Get result - no longer sorting subcategories
    const result = Array.from(categoryMap.values());

    // Sort only the main categories
    return result.sort((a, b) => a.name.localeCompare(b.name));
  }, [isAuthenticated, categories, publicProductsData?.categories]);

  // Update cached category structure whenever we get new ones - fix dependency cycle
  useEffect(() => {
    // Only update if there are actual changes to prevent unnecessary renders
    if (
      !isAuthenticated &&
      publicCategories.length > 0 &&
      JSON.stringify(cachedPublicCategories) !==
        JSON.stringify(publicCategories)
    ) {
      setCachedPublicCategories(publicCategories);
    }
  }, [publicCategories, isAuthenticated, cachedPublicCategories]);

  // Use cached categories if current ones are empty - memoize to prevent recreation on each render
  const effectivePublicCategories = useMemo(() => {
    return !isAuthenticated && publicCategories.length === 0
      ? cachedPublicCategories
      : publicCategories;
  }, [isAuthenticated, publicCategories, cachedPublicCategories]);

  // Handle category display
  let displayedCategories: string[] = [];

  const allSubCategoryNames = new Set<string>();

  // Collect all subcategory names
  if (isAuthenticated) {
    categories.forEach((cat) => {
      cat.subCategories.forEach((subCat) => {
        if (subCat.name) allSubCategoryNames.add(subCat.name);
      });
    });
  } else {
    effectivePublicCategories.forEach((cat) => {
      cat.subCategories.forEach((subCat) => {
        if (subCat.name) allSubCategoryNames.add(subCat.name);
      });
    });
  }

  // Filter categories to only include top-level ones (not subcategories)
  if (isAuthenticated) {
    // For authenticated users with restrictions
    if (hasRestrictions) {
      displayedCategories = categoryNames
        .filter((name): name is string => Boolean(name))
        .filter((name) => !allSubCategoryNames.has(name))
        .sort((a, b) => a.localeCompare(b)); // Sort alphabetically
    } else {
      // For authenticated users without restrictions
      displayedCategories = allCategoryNames
        .filter((name): name is string => Boolean(name))
        .filter((name) => !allSubCategoryNames.has(name))
        .sort((a, b) => a.localeCompare(b)); // Sort alphabetically
    }
  } else {
    // For unauthenticated users
    displayedCategories = allCategoryNames
      .filter((name): name is string => Boolean(name))
      .filter((name) => !allSubCategoryNames.has(name))
      .sort((a, b) => a.localeCompare(b)); // Sort alphabetically
  }

  // Get the sorted products based on category and parent category (API now handles search filtering)
  const sortedProducts = useMemo(() => {
    if (!products || products.length === 0) return [];

    /**
     * TEMPORARY SOLUTION: Custom sorting for valve products based on SKU
     *
     * This block checks if any products match our valve SKUs and sorts them
     * according to the priority order defined above. This is a temporary solution
     * until a proper backend sorting implementation is available.
     */

    // Check for any products with our target Valve SKUs (UC4*, UC1A, UC2A)
    // const hasValveProducts = products.some((product) => {
    //   const sku = (product.sku || "").trim().toUpperCase();
    //   // Check using both exact and prefix matches
    //   return PRIORITY_SKUS.some(
    //     (prioritySku) => sku === prioritySku || sku.startsWith(prioritySku)
    //   );
    // });

    // // If we detect any valve products, apply our custom sorting
    // if (hasValveProducts) {
    //   console.log("TEMPORARY FIX: Applying custom valve product sorting");
    //   return sortProductsBySkuPriority(products);
    // }

    // // If no valve products detected, sort by creation date (newest first)
    // return [...products].sort((a, b) => {
    //   const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
    //   const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;

    //   return dateA - dateB; // Oldest first
    // });

    // Sort products by its configured position in the producct_arrangement data.
    // This eliminates the sorting of products by hardcoded values and by "created_at" field.
    return products.sort((a, b) => {
      const aPos = a.product_arrangements.at(0)?.position ?? -1;
      const bPos = b.product_arrangements.at(0)?.position ?? -1;

      return aPos - bPos;
    });
  }, [products]);

  return (
    <div className="container max-w-9xl mx-auto pb-10">
      <h1 className="text-4xl mb-6">Products</h1>

      <div className="flex flex-col md:flex-row gap-12">
        {/* Left side - Category Tabs */}
        <div className="md:w-80 flex-shrink-0 px-2 py-4">
          <div className="relative w-full space-y-4">
            <div className="mb-4">
              <form
                onSubmit={handleSearch}
                className="relative flex"
                role="search"
              >
                <Input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full border !border-primary/80 !pr-10 !py-2 focus:!border-primary focus:!ring-0 focus:!ring-primary focus-visible:!ring-offset-0 !rounded-none !rounded-l-sm"
                  aria-label="Search products"
                />
                <Button
                  type="submit"
                  className="!rounded-none !rounded-r-sm !py-2 !px-3 bg-primary text-white hover:bg-primary/90"
                  aria-label="Submit search"
                >
                  <Search className="h-4 w-4" />
                </Button>
                {debouncedSearchTerm && (
                  <Button
                    type="button"
                    variant="ghost"
                    className="absolute right-12 top-1/2 transform -translate-y-1/2 h-full bg-transparent hover:bg-transparent hover:text-primary hover:font-bold"
                    onClick={clearSearch}
                    aria-label="Clear search"
                  >
                    &#x2715;
                  </Button>
                )}
              </form>
            </div>

            <div className="h-full w-full dark:bg-neutral-900 bg-white p-6 shadow-sm border border-gray-200 dark:border-neutral-800">
              <h3 className="text-xl font-bold mb-5 text-primary border-b border-gray-200 dark:border-neutral-800 pb-2">
                Categories
              </h3>

              <div className="w-full flex flex-col items-start justify-start p-0 gap-2">
                {/* Always show "All Products" at the top */}
                <button
                  className={cn(
                    "flex justify-between items-center w-full text-lg h-fit text-black border-none",
                    activeCategory === null &&
                      "text-primary bg-primary/10 font-medium",
                    "hover:bg-primary/5 transition-all duration-200 ease-in-out py-2 px-3"
                  )}
                  onClick={() => handleCategoryChange(null)}
                >
                  <span>All Products</span>
                </button>

                {/* Render sorted categories */}
                {displayedCategories.map((category) => {
                  if (!category) return null;

                  // Use the appropriate category structure based on authentication status
                  let subCategories: Category[] = [];
                  if (isAuthenticated) {
                    subCategories =
                      categories.find((cat) => cat.name === category)
                        ?.subCategories ?? [];
                  } else {
                    subCategories =
                      effectivePublicCategories.find(
                        (cat) => cat.name === category
                      )?.subCategories ?? [];
                  }

                  return (
                    <CategoryTree
                      key={`category-${category}`}
                      name={category}
                      subCategories={subCategories}
                      onCategorySelect={handleCategoryChange}
                      isParent={true}
                      isActive={activeCategory === category}
                      totalItems={totalItems}
                    />
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Product Display */}
        <div className="flex-1">
          <div className="flex justify-between items-center pb-20">
            <h2 className="text-4xl font-medium">
              {activeCategory ? activeCategory : "All Products"}
            </h2>
            <span className="text-sm font-bold text-muted-foreground">
              {totalItems > ITEMS_PER_PAGE &&
                `Showing ${(currentPage - 1) * ITEMS_PER_PAGE + 1}-${Math.min(
                  currentPage * ITEMS_PER_PAGE,
                  totalItems
                )} of ${totalItems}`}
            </span>
          </div>

          {shouldLoadSkeleton ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {productSkeletons}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {sortedProducts && sortedProducts.length > 0 ? (
                  sortedProducts.map((product) => {
                    const options = parseProductOptions(
                      product.options
                        ? (product.options as unknown as ProductOptions)
                        : []
                    );
                    const productGroupPrice =
                      product.product_group_prices?.find(
                        (price) =>
                          price.group_id === customerData?.customer?.group?.id
                      );

                    const customerHasDiscount =
                      isAuthenticated && productGroupPrice !== null;

                    const discountedPrice = customerHasDiscount
                      ? productGroupPrice?.custom_price ?? product.price
                      : product.price;

                    return (
                      <ProductCard
                        isQuote={product.is_quote}
                        key={product.id}
                        id={product.id}
                        name={product.name}
                        sku={product.sku ?? ""}
                        price={discountedPrice}
                        image={product.image ?? ""}
                        variants={[]}
                        slug={product.id}
                        categories={
                          (product.product_categories
                            ?.map((cat) => cat.category_id?.name)
                            .filter(Boolean) as string[]) || []
                        }
                        options={options}
                        customerData={customerData?.customer}
                        productCategories={product.product_categories || []}
                        currentCategory={activeCategory}
                      />
                    );
                  })
                ) : shouldLoadSkeleton ||
                  isCategoryChanging ||
                  (isLoading && products.length === 0) ? (
                  <div className="col-span-full">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {productSkeletons}
                    </div>
                  </div>
                ) : (
                  <div className="col-span-full min-h-sm flex items-center justify-center flex-col gap-4">
                    <div className="flex flex-col items-center justify-center gap-4">
                      <PackageX className="h-24 w-24 text-muted-foreground" />
                      <p className="text-muted-foreground font-medium text-3xl">
                        No products found.
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {totalItems > 0 && (
                <div className="flex items-center justify-center gap-2 mt-8">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      // Set to first page
                      setCurrentPage(1);

                      // Update URL
                      router.push(
                        {
                          pathname: router.pathname,
                          query: {
                            ...(activeCategory
                              ? { category: activeCategory }
                              : {}),
                            ...(debouncedSearchTerm
                              ? { search: debouncedSearchTerm }
                              : {}),
                            page: "1",
                          },
                        },
                        undefined,
                        { shallow: true }
                      );
                    }}
                    disabled={currentPage === 1}
                  >
                    <ChevronsLeft className="h-4 w-4" />
                    <span className="sr-only">First page</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={prevPageFn}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    <span className="sr-only">Previous page</span>
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={nextPageFn}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                    <span className="sr-only">Next page</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      // Set to last page
                      setCurrentPage(totalPages);

                      // Update URL
                      router.push(
                        {
                          pathname: router.pathname,
                          query: {
                            ...(activeCategory
                              ? { category: activeCategory }
                              : {}),
                            ...(debouncedSearchTerm
                              ? { search: debouncedSearchTerm }
                              : {}),
                            page: totalPages.toString(),
                          },
                        },
                        undefined,
                        { shallow: true }
                      );
                    }}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronsRight className="h-4 w-4" />
                    <span className="sr-only">Last page</span>
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

interface CategoryTreeProps {
  name: string;
  subCategories: CategoryTreeProps[];
  onCategorySelect?: (category: string) => void;
  isParent?: boolean;
  isActive?: boolean;
  totalItems?: number;
}

function CategoryTree({
  name,
  subCategories,
  onCategorySelect,
  totalItems,
  isActive,
  isParent = false,
}: CategoryTreeProps) {
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);
  const currentCategory = router.query.category as string | undefined;
  const hasSubCategories = subCategories.length > 0;

  // Fix the infinite update loop by only running the effect when the dependencies actually change
  // and by being more careful with the recursive check
  useEffect(() => {
    let shouldExpand = false;

    if (currentCategory === name) {
      shouldExpand = true;
    } else if (
      hasSubCategories &&
      subCategories.some((sub) => sub.name === currentCategory)
    ) {
      // Only check immediate children - avoids deep recursion that can cause problems
      shouldExpand = true;
    }

    if (shouldExpand && !isExpanded) {
      setIsExpanded(true);
    }
  }, [currentCategory, name, hasSubCategories, subCategories, isExpanded]);

  const handleCategoryClick = (e: React.MouseEvent) => {
    // Prevent the default behavior to avoid unexpected navigation
    e.preventDefault();

    if (hasSubCategories) {
      // For categories with subcategories, only toggle the expansion state
      // without selecting the category for product display
      setIsExpanded(!isExpanded);
    } else if (onCategorySelect) {
      // Only select category for product display if it has no subcategories
      onCategorySelect(name);
    }
  };

  // Helper function to handle subcategory clicks
  const handleSubcategoryClick = (subcategoryName: string) => {
    if (onCategorySelect) {
      onCategorySelect(subcategoryName);
    }
  };

  return (
    <div className="relative w-full h-fit flex flex-col items-start justify-start gap-2 border-none">
      <div className="w-full flex items-center border-none">
        <button
          className={cn(
            "flex justify-between items-center w-full text-md h-fit text-black border-none",
            !hasSubCategories && !isParent && "text-xs pl-4",
            currentCategory === name &&
              "text-primary bg-primary/10 font-medium",
            hasSubCategories
              ? "cursor-pointer hover:bg-primary/5"
              : "hover:bg-primary/5",
            "transition-all duration-200 ease-in-out py-2 px-3"
          )}
          onClick={handleCategoryClick}
        >
          <div className="relative flex items-center justify-between w-full truncate">
            <div className="flex items-center">
              <span>{name}</span>
              {isActive ? (
                <span className="total-items text-xs text-muted-foreground font-medium ml-2 bg-primary/10 px-[10px] py-1 rounded-full">
                  {totalItems}
                </span>
              ) : null}
            </div>

            {hasSubCategories && (
              <ChevronDown
                className={cn(
                  "h-4 w-4 transition-transform duration-200",
                  isExpanded ? "rotate-180" : ""
                )}
              />
            )}
          </div>
        </button>
      </div>

      {isExpanded && hasSubCategories && (
        <div className="pl-4 w-full h-fit flex flex-col gap-1.5 pt-1 pb-2">
          {subCategories.map((sub) => (
            <button
              key={sub.name}
              className={cn(
                "w-full text-left cursor-pointer px-3 py-1.5 text-sm transition-colors",
                "hover:bg-primary/5",
                currentCategory === sub.name &&
                  "text-primary bg-primary/10 font-medium"
              )}
              onClick={() => handleSubcategoryClick(sub.name)}
            >
              {sub.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
